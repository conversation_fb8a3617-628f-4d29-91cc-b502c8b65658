extends Control

enum STATES {CharSelect, Menu}
var state = STATES.Menu
@onready var character_select_scene = $CharacterSelect
@onready var name_button = $Menu/NameButton
@onready var name_popup = $Menu/NamePopup
@onready var character_preview = $CharacterPreview
@onready var settings_popup = $Menu/SettingsPopup
@onready var leaderboard_popup = $Menu/LeaderboardPopup
@onready var print_label = $Menu/Label
@onready var shop_button = $Menu/ShopButton
@onready var leaderboard_button = $Menu/LeaderboardButton
@onready var rate_popup = $Menu/RatePopup
@onready var exit_popup = $Menu/ExitPopup
@onready var vpn_popup = $Menu/VPNPopup
@onready var chat_button = $Menu/ChatButton
@onready var chat_popup = $Menu/PublicChatPopup
@onready var character_preview_image = $Menu/CharacterPreviewImage
@onready var progress_button = $Menu/ProgressButton
@onready var progress_popup = $Menu/ProgressPopup
@onready var tax_parent = $Menu/PlayButton/TaxParent
@onready var game_tax = $Menu/PlayButton/TaxParent/GameTax
@onready var not_enough_coins_popup = $Menu/NotEnoughCoinsPopup
@onready var disconnect_popup = $Menu/DisconnectPopup
@onready var loading_panel = $Menu/LoadingPanel
@onready var update_popup: Control = $Menu/UpdatePopup
@onready var username_label: Label = $Menu/UsernameLabel/ClipboardButton/UsernameLabel
@onready var gift_popup: Control = $Menu/GiftPopup
@onready var play_button = $Menu/PlayButton
@onready var game_mode_selector_button = $Menu/GameModeSelectorButton

# Game mode constants
var game_mode_names = ["RACEMODE", "FREERIDEMODE", "PROPHAUNTMODE"]


#Hints
var disable_tutorial = true
@onready var tutorial_hint = $HintParent/TutorialHint
@onready var first_game_hint = $HintParent/FirstGameHint
@onready var daily_reward_hint = $HintParent/DailyRewardHint
@onready var character_change_hint = $HintParent/CharacterHint
@onready var second_game_hint = $HintParent/SecondGameHint
@onready var chat_tutorial_popup = $Menu/ChatTutorialPopup



func _ready():
	SoundManager.update_music_volume()
	SoundManager.update_sound_volume()
	username_label.text = DataSaver.get_item("username", "")
	update_popup.visible = false
	ClientBackendManager.force_update.connect(force_update)
	var come_from_race = false
	if Selector.selected_game_mode == Constants.GameMode.Race:
		come_from_race = true

	Selector.selected_game_mode = Constants.GameMode.FreeRide
	StageManager.me_eliminated = false
	StageManager.current_stage = 1
	play_bg_music()
	name_popup.visible = false
	Selector.init()
	exit_popup.quit.connect(on_quit)
	Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
	name_button.set_my_name(DataSaver.get_item("handle", ""))
	if Constants.is_headless:
		_on_play_button_pressed()
	character_select_scene.hide_all()
	character_preview_image.camera_changed.connect(character_preview.on_camera_changed)
	progress_popup.character_unlocked.connect(on_progress_character_unlocked)
	shop_button.text = "SHOP"
	$Menu/PlayButton.text = "PLAY"
	chat_button.text = "CHAT"
	tax_parent.visible = false
	ClientBackendManager.sync_finished.connect(on_sync_finished)
	NativeMarket.print_signal.connect(print_my_debug)
	NativeMarket.init()
	handle_rate_popup()
	update_game_mode_display()
	NakamaManager.socket_connected.connect(on_nakama_connected)
	if NakamaManager._is_connected:
		on_nakama_connected()
	
	
	if Selector.should_start_game_scene:
		Selector.should_start_game_scene = false
		get_tree().change_scene_to_file("res://Scenes/game_scene.tscn")
		return
	
	gift_popup.check_gifts()
	if Selector.need_to_sync:
		ClientBackendManager.send_sync_request()
		progress_button.set_loading(true)
		Selector.need_to_sync = false
	else:
		progress_button.set_loading(false)
	
	if Selector.is_disconnected_from_game:
		Selector.is_disconnected_from_game = false
		disconnect_popup.show_popup()
	else:
		handle_hints()
	Selector.is_exit_game = false
	character_preview_image.preview = character_preview
	loading_panel.visible = true
	await Constants.wait_frames(1)
	character_preview.update(character_select_scene.init())
	loading_panel.visible = false
	
	if come_from_race:
		pass
	
	Selector.presence = Constants.PRESENCE_MAIN_MENU
	ClanManager.change_hud_parent(self)


func ask_for_name():
	var handle = DataSaver.get_item("handle", "Player")
	if handle.begins_with("Player") and DataSaver.get_item("name_ask", false) == false:
		name_popup.show_popup()
		DataSaver.set_item("name_ask", true, true)
		return true
	return false


func handle_rate_popup():
	if NativeMarket.market == NativeMarket.MarketType.Zarinpal:
		return
	var today_win = DataSaver.get_item("today_win", false)
	today_win = true
	if  today_win:
		if DataSaver.get_item("has_rate", false) == false:
			var rate_cup = DataSaver.get_item("rate_cup", 0)
			for cup in Constants.RATE_CUPS:
				if rate_cup >= cup:
					continue
				if Selector.my_cup >= cup:
					DataSaver.set_item("rate_show", true, false)
					DataSaver.set_item("rate_cup", cup, true)
					rate_popup.visible = true
					return


func _on_character_select_button_pressed():
	state = STATES.CharSelect
	character_select_scene.show_all()
	$Menu.visible = false


func _on_character_select_finished():
	$Menu.visible = true
	state = STATES.Menu


func _on_play_button_pressed():
	Firebase.logEvent("MainMenuPlay", {})
	Selector.my_name = name_button.get_my_name()

	#if Selector.my_coin < Selector.game_tax:
	#	not_enough_coins_popup.show_popup()
	#else:
	play_game()


func play_game():
	NakamaManager.leave_public_chat()

	var selected_mode = DataSaver.get_item("selected_game_mode", -1)
	if selected_mode == -1:
		# No game mode selected, show selector
		show_game_mode_selector()
	else:
		# Game mode already selected, start the game
		start_selected_game_mode(selected_mode)


func start_selected_game_mode(mode_index: int):
	match mode_index:
		0: # Race
			play_race_mode()
		1: # Free Ride
			play_freeride_mode()
		2: # Prophaunt
			play_prophaunt_mode()
		_:
			# Invalid mode, show selector
			show_game_mode_selector()


func play_race_mode():
	get_tree().change_scene_to_file("res://Scenes/game_scene.tscn")
	DataSaver.set_item("handle", name_button.get_my_name(), false)
	DataSaver.send_save_request()


func play_freeride_mode():
	get_tree().change_scene_to_file("res://Scenes/FreeRide/freeride_server_selector.tscn")
	DataSaver.set_item("handle", name_button.get_my_name(), false)
	DataSaver.send_save_request()


func play_prophaunt_mode():
	get_tree().change_scene_to_file("res://prophaunt/scenes/ProphauntGame.tscn")
	DataSaver.set_item("handle", name_button.get_my_name(), false)
	DataSaver.send_save_request()


var game_mode_selector_scene = preload("res://Scenes/game_mode_selector.tscn")
var game_mode_selector_instance = null

func _on_game_mode_selector_back():
	hide_game_mode_selector()


func hide_game_mode_selector():
	if game_mode_selector_instance:
		game_mode_selector_instance.visible = false


func _on_name_button_pressed():
	Firebase.logEvent("MainMenuName", {})
	name_popup.show_popup()


@warning_ignore("shadowed_variable_base_class")
func _on_name_popup_on_name_changed(name):
	DataSaver.set_item("handle", name, false)
	name_button.set_my_name(name)
	Selector.my_name = name



func _on_character_select_character_selected(selected_char):
	if character_preview:
		loading_panel.visible = true
		await Constants.wait_frames(2)
		character_preview.update(selected_char)
		loading_panel.visible = false
		Selector.last_selected_char_id = selected_char["id"]


func play_bg_music():
	SoundManager.play_bg_music()


func _on_settings_button_pressed():
	Firebase.logEvent("MainMenuSettings", {})
	settings_popup.popup()
	#NativeMarket.test_crash()


func _on_settings_popup_music_toggled(toggle):
	if toggle == false:
		SoundManager.stop_bg_music()
	elif not SoundManager.bg_player.playing:
		SoundManager.play_bg_music()


func _on_leaderboard_button_pressed():
	Firebase.logEvent("MainMenuLeaderboard", {})
	leaderboard_popup.show_popup()


func print_my_debug(txt):
	print("print debug ", txt)
	print_label.text = print_label.text + "\n" + txt


func _on_shop_button_pressed():
	Firebase.logEvent("MainMenuShop", {})
	Selector.shop_mode = Selector.SHOP_ENUM.Coin
	get_tree().change_scene_to_file("res://Scenes/shop_scene.tscn")


func _on_coin_show_pressed():
	Firebase.logEvent("MainMenuCoin", {})
	_on_shop_button_pressed()


func _on_change_character_button_pressed():
	Firebase.logEvent("MainMenuCharacterSelect", {})
	_on_character_select_button_pressed()
	character_change_hint.visible = false


func _notification(what: int) -> void:
	if what == NOTIFICATION_WM_CLOSE_REQUEST:
		# For Windows
		on_goback()
	if what == NOTIFICATION_WM_GO_BACK_REQUEST:
		# For android
		on_goback()
	if what == NOTIFICATION_APPLICATION_RESUMED:
		ClientBackendManager.send_sync_request()


func on_goback():
	exit_popup.popup()


func on_quit():
	get_tree().quit()


func _on_daily_reward_button_pressed():
	#daily_reward_popup.show_popup()
	#daily_reward_hint.visible = false
	pass


func _process(_delta):
	if Input.is_action_just_pressed("exit"):
		on_goback()
	
	if not Constants.is_headless and Constants.is_desktop():
		Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)


func _on_tutorial_button_pressed():
	get_tree().change_scene_to_file("res://Scenes/tutorial_scene.tscn")


func _on_vpn_popup_play():
	play_game()


func _on_chat_button_pressed():
	Firebase.logEvent("MainMenuChat", {})
	if DataSaver.get_item("HINT_CHAT", false) == false:
		chat_tutorial_popup.visible = true
		DataSaver.set_item("HINT_CHAT", true, true)
		return
	chat_popup.show_popup()


func on_nakama_connected():
	chat_popup.request_last_messages()


func on_sync_finished():
	progress_button.set_loading(false)


func _on_progress_button_pressed():
	progress_popup.show_popup()


func handle_hints():
	if ask_for_name():
		return

	if Selector.my_cup > 30:
		return
	
	if disable_tutorial:
		return #Disable Tutorial Hints

	if DataSaver.get_item("HINT_FIRST_GAME", false) == false:
		first_game_hint.show_hint()
		DataSaver.set_item("HINT_FIRST_GAME", true, true)
		return
	
	if DataSaver.get_item("HINT_DAILY_REWARD", false) == false:
		daily_reward_hint.show_hint()
		DataSaver.set_item("HINT_DAILY_REWARD", true, true)
		return
	
	if DataSaver.get_item("HINT_SECOND_GAME", false) == false:
		second_game_hint.show_hint()
		DataSaver.set_item("HINT_SECOND_GAME", true, true)
		return


func _on_name_popup_hidden():
	handle_hints()


func _on_daily_reward_popup_hidden():
	DataSaver.set_item("HINT_DAILY_REWARD", true, true)
	handle_hints()


func _on_character_select_hidden():
	handle_hints()


func on_progress_character_unlocked():
	character_select_scene.on_character_unlocked()


func _on_chat_tutorial_popup_hidden():
	chat_popup.show_popup()


func force_update():
	update_popup.show_popup(true)


func _on_clipboard_button_pressed() -> void:
	DisplayServer.clipboard_set(username_label.text)
	NativeFunctions.toast(tr("COPIED"))


func _on_rubika_button_pressed() -> void:
	OS.shell_open("https://rubika.ir/animalrush")


func _on_aparat_button_pressed() -> void:
	OS.shell_open("https://www.aparat.com/animalrushgame")


func _on_instagram_button_pressed() -> void:
	OS.shell_open("https://www.instagram.com/animalrushgame")


func _on_clan_button_pressed() -> void:
	ClanManager.hud.show_popup()


func _exit_tree() -> void:
	ClanManager.change_hud_parent_to_self()


func _on_clan_badge_pressed(_id: int) -> void:
	_on_clan_button_pressed()


func update_game_mode_display():
	var selected_mode = DataSaver.get_item("selected_game_mode", -1)
	if selected_mode == -1:
		play_button.text = tr("PLAY")
	else:
		if selected_mode >= 0 and selected_mode < game_mode_names.size():
			play_button.text = tr("PLAY") + "\n" + tr(game_mode_names[selected_mode])
		else:
			play_button.text = tr("PLAY")


func _on_game_mode_selector_button_pressed():
	show_game_mode_selector()


func show_game_mode_selector():
	if game_mode_selector_instance == null:
		game_mode_selector_instance = game_mode_selector_scene.instantiate()
		add_child(game_mode_selector_instance)
		game_mode_selector_instance.game_mode_selected.connect(_on_game_mode_selected)
		game_mode_selector_instance.back_pressed.connect(_on_game_mode_selector_back)
	game_mode_selector_instance.visible = true


func _on_game_mode_selected(mode_index: int):
	# Save the selected game mode
	DataSaver.set_item("selected_game_mode", mode_index)
	DataSaver.send_save_request()

	# Update the display
	update_game_mode_display()

	# Start the selected game mode immediately
	start_selected_game_mode(mode_index)
