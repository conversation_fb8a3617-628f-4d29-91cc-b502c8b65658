[gd_scene load_steps=56 format=3 uid="uid://cbkky64psu3on"]

[ext_resource type="Script" path="res://Scenes/main_menu.gd" id="1_8bxx6"]
[ext_resource type="PackedScene" uid="uid://br6smnmcr4weh" path="res://Scenes/character_select.tscn" id="2_kayod"]
[ext_resource type="Texture2D" uid="uid://dxe6urc0q6q75" path="res://Scenes/ui/assets/main-menubg.PNG" id="3_q0f3c"]
[ext_resource type="Script" path="res://Scenes/CharacterPreviewImage.gd" id="4_5m8xs"]
[ext_resource type="PackedScene" uid="uid://cs2n3utn6s0fd" path="res://Scenes/ui/name_button.tscn" id="4_80aeo"]
[ext_resource type="PackedScene" uid="uid://bjbx34eycyyqr" path="res://Scenes/ui/name_popup.tscn" id="5_ep3jm"]
[ext_resource type="PackedScene" uid="uid://b3elqmgq54s52" path="res://Scenes/ui/loading_element.tscn" id="5_k6fwt"]
[ext_resource type="PackedScene" uid="uid://dimgwrlco20uw" path="res://Scenes/ui/custom_button_grey.tscn" id="5_yqo4e"]
[ext_resource type="Texture2D" uid="uid://dg025q3wg2q5r" path="res://Scenes/ui/assets/leader.png" id="6_dc03a"]
[ext_resource type="PackedScene" uid="uid://dd457amu0axmg" path="res://Scenes/Mobile/Apps/Clan/ClanBadge.tscn" id="7_3x438"]
[ext_resource type="PackedScene" uid="uid://c0bvc7tlsk7xo" path="res://Scenes/ui/character_preview.tscn" id="7_7bjhp"]
[ext_resource type="Script" path="res://Scenes/ui/MenuClanButton.gd" id="7_brraq"]
[ext_resource type="Texture2D" uid="uid://dfc3jhy827xtq" path="res://Scenes/ui/assets/shop.png" id="7_gnheb"]
[ext_resource type="Texture2D" uid="uid://b010qsgl38ikk" path="res://Scenes/ui/assets/coin1.png" id="7_m2chx"]
[ext_resource type="Script" path="res://Scenes/ui/change_character_button.gd" id="8_0tmbb"]
[ext_resource type="PackedScene" uid="uid://ckvqr6ls4dsak" path="res://Scenes/ui/SettingsButton.tscn" id="8_yp623"]
[ext_resource type="Texture2D" uid="uid://dftlr387vc7jb" path="res://Scenes/ui/assets/chat.png" id="9_cjknp"]
[ext_resource type="Script" path="res://Scenes/ui/chat_button.gd" id="9_fvaof"]
[ext_resource type="PackedScene" uid="uid://dp5wkmqu5h3hs" path="res://Scenes/ui/SettingsPopup.tscn" id="9_xpvwf"]
[ext_resource type="Texture2D" uid="uid://cjj265quiquct" path="res://Scenes/ui/assets/mobile/clan.png" id="10_jwh12"]
[ext_resource type="Texture2D" uid="uid://13njmhdsahvy" path="res://assets/characters/2D Card/Bear_card.png" id="10_k8t2m"]
[ext_resource type="FontFile" uid="uid://djnxbn02v2032" path="res://Scenes/ui/assets/Font/Lalezar-Regular.ttf" id="11_gn1ql"]
[ext_resource type="PackedScene" uid="uid://6uthrynmlmlj" path="res://Scenes/ui/leaderboard_popup.tscn" id="11_k7af7"]
[ext_resource type="PackedScene" uid="uid://b0ypg3pguoouw" path="res://Scenes/ui/cup_show.tscn" id="13_3g6nl"]
[ext_resource type="Texture2D" uid="uid://de3w8tdrgipgg" path="res://Scenes/ui/assets/new_badge.png" id="14_4moyu"]
[ext_resource type="PackedScene" uid="uid://dfw3vd3t4jtva" path="res://Scenes/ui/coin_show.tscn" id="14_t3uge"]
[ext_resource type="PackedScene" uid="uid://dmmhb6de6hkm4" path="res://Scenes/ui/rate_popup.tscn" id="16_81y72"]
[ext_resource type="PackedScene" uid="uid://cvnuejwsey787" path="res://Scenes/ui/exit_popup.tscn" id="17_l5v2e"]
[ext_resource type="PackedScene" uid="uid://cup27grd6a3hq" path="res://Scenes/ui/vpn_popup.tscn" id="20_siohu"]
[ext_resource type="PackedScene" uid="uid://bel7cn8dknadw" path="res://Scenes/ui/crown_show.tscn" id="20_wmpg2"]
[ext_resource type="PackedScene" uid="uid://3367aq55lqr" path="res://Scenes/ui/chat/public_chat_popup.tscn" id="21_6f4v1"]
[ext_resource type="PackedScene" uid="uid://c6pme0mxv1pdf" path="res://Scenes/ui/progress_button.tscn" id="23_dlmr0"]
[ext_resource type="PackedScene" uid="uid://ckp78b1c6esl7" path="res://Scenes/ui/progress_popup.tscn" id="24_vf3d6"]
[ext_resource type="Script" path="res://Scenes/ui/CustomButton.gd" id="25_2e2w0"]
[ext_resource type="Texture2D" uid="uid://0pg6iqc4n45o" path="res://Scenes/ui/assets/yellow.png" id="26_taxne"]
[ext_resource type="Texture2D" uid="uid://b3kqt1hat8wiw" path="res://Scenes/ui/assets/instagram.png" id="26_yjqb1"]
[ext_resource type="PackedScene" uid="uid://cpski0pwvvep5" path="res://Scenes/ui/not_enough_coins_popup.tscn" id="30_8st3f"]
[ext_resource type="PackedScene" uid="uid://dkwe6wi8ed6tl" path="res://Scenes/ui/disconnect_popup.tscn" id="30_t5jej"]
[ext_resource type="PackedScene" uid="uid://k41nkgt8rnks" path="res://Scenes/ui/ui_hint.tscn" id="31_tx7vi"]
[ext_resource type="Script" path="res://Scenes/ui/HintManager.gd" id="31_xx3io"]
[ext_resource type="PackedScene" uid="uid://cfnc1rsessbhp" path="res://Scenes/ui/chat_tutorial_popup.tscn" id="32_vewh7"]
[ext_resource type="PackedScene" uid="uid://jqbc5awj1g5t" path="res://Scenes/ui/CustomButton.tscn" id="35_uqxyy"]
[ext_resource type="Texture2D" uid="uid://cfg3qf3hhytrh" path="res://Scenes/ui/assets/whitebutton.png" id="36_8p6sa"]
[ext_resource type="PackedScene" uid="uid://b1sc2a7o0mv5w" path="res://Scenes/ui/update_popup.tscn" id="36_an2a6"]
[ext_resource type="Texture2D" uid="uid://cw0ou8f3ia7b4" path="res://Scenes/ui/assets/gray.png" id="37_6371e"]
[ext_resource type="Texture2D" uid="uid://dhi86y5esekma" path="res://Scenes/ui/assets/rubika.png" id="38_pgx4r"]
[ext_resource type="Texture2D" uid="uid://cvls34vy0ccpj" path="res://Scenes/ui/assets/aparat.png" id="39_unswq"]
[ext_resource type="PackedScene" uid="uid://cfwqh3dw50tb6" path="res://Scenes/ui/GiftPopup.tscn" id="43_rohdu"]

[sub_resource type="ViewportTexture" id="ViewportTexture_iqjfn"]
viewport_path = NodePath(".")

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_egcvn"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_fmyl7"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_7l4b6"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_8endb"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_6pnhn"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_l80g8"]
bg_color = Color(0, 0, 0, 0.882353)

[node name="MainMenu" type="Control"]
layout_direction = 2
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_8bxx6")
metadata/_edit_lock_ = true

[node name="CharacterPreview" parent="." instance=ExtResource("7_7bjhp")]
own_world_3d = true
transparent_bg = true
size = Vector2i(1200, 720)

[node name="BG" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("3_q0f3c")
expand_mode = 2
stretch_mode = 6
metadata/_edit_lock_ = true

[node name="Menu" type="Control" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
metadata/_edit_lock_ = true

[node name="CharacterPreviewImage" type="TextureRect" parent="Menu"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 5.0
offset_top = -68.0
offset_right = 5.0
offset_bottom = -218.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("ViewportTexture_iqjfn")
expand_mode = 2
stretch_mode = 5
script = ExtResource("4_5m8xs")
metadata/_edit_lock_ = true

[node name="LoadingElement" parent="Menu/CharacterPreviewImage" instance=ExtResource("5_k6fwt")]
visible = false
custom_minimum_size = Vector2(40, 40)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -19.9999
offset_top = 69.0
offset_right = 20.0001
offset_bottom = 109.0

[node name="ClanButton" parent="Menu/CharacterPreviewImage" instance=ExtResource("35_uqxyy")]
custom_minimum_size = Vector2(83, 125)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = 99.5
offset_top = -6.5
offset_right = 182.5
offset_bottom = 118.5
pivot_offset = Vector2(41.5, 37.5)
script = ExtResource("7_brraq")

[node name="ClanBadge" parent="Menu/CharacterPreviewImage/ClanButton" instance=ExtResource("7_3x438")]
layout_mode = 1
offset_right = 0.0
offset_bottom = 0.0

[node name="ClanLabel" type="Label" parent="Menu/CharacterPreviewImage/ClanButton"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -86.0
offset_top = 1.5
offset_right = 86.0
offset_bottom = 68.5
grow_horizontal = 2
grow_vertical = 2
theme_override_fonts/font = ExtResource("11_gn1ql")
theme_override_font_sizes/font_size = 29
text = "CLAN"
horizontal_alignment = 1
vertical_alignment = 1
clip_text = true

[node name="NameButton" parent="Menu" instance=ExtResource("4_80aeo")]
custom_minimum_size = Vector2(300, 88)
layout_mode = 1
offset_left = 80.0
offset_top = 18.0
offset_right = 380.0
offset_bottom = 106.0

[node name="LeaderboardButton" parent="Menu" instance=ExtResource("5_yqo4e")]
custom_minimum_size = Vector2(100, 88)
layout_mode = 1
anchors_preset = 0
anchor_right = 0.0
anchor_bottom = 0.0
offset_left = 399.0
offset_top = 18.0
offset_right = 499.0
offset_bottom = 106.0
grow_horizontal = 1
grow_vertical = 1
pivot_offset = Vector2(50, 44)

[node name="TextureRect" type="TextureRect" parent="Menu/LeaderboardButton"]
layout_mode = 0
offset_left = 31.0
offset_top = 6.0
offset_right = 67.0
offset_bottom = 43.0
mouse_filter = 2
texture = ExtResource("6_dc03a")
expand_mode = 1
stretch_mode = 4

[node name="Label" type="Label" parent="Menu/LeaderboardButton"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -29.0
offset_top = -53.0
offset_right = 29.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 3
theme_override_fonts/font = ExtResource("11_gn1ql")
theme_override_font_sizes/font_size = 25
text = "LEADERBOARD"
horizontal_alignment = 1

[node name="ClanButton" parent="Menu" instance=ExtResource("5_yqo4e")]
visible = false
custom_minimum_size = Vector2(100, 88)
layout_mode = 1
anchors_preset = 0
anchor_right = 0.0
anchor_bottom = 0.0
offset_left = 511.0
offset_top = 18.0
offset_right = 611.0
offset_bottom = 106.0
grow_horizontal = 1
grow_vertical = 1
pivot_offset = Vector2(50, 44)

[node name="TextureRect" type="TextureRect" parent="Menu/ClanButton"]
layout_mode = 0
offset_left = 24.0
offset_right = 81.0
offset_bottom = 55.0
mouse_filter = 2
texture = ExtResource("10_jwh12")
expand_mode = 1
stretch_mode = 4

[node name="Label" type="Label" parent="Menu/ClanButton"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -29.0
offset_top = -44.0
offset_right = 29.0
offset_bottom = -1.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 3
theme_override_fonts/font = ExtResource("11_gn1ql")
theme_override_font_sizes/font_size = 25
text = "CLAN"
horizontal_alignment = 1

[node name="ChangeCharacterButton" parent="Menu" instance=ExtResource("5_yqo4e")]
custom_minimum_size = Vector2(230, 60)
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_right = 0.0
anchor_bottom = 0.5
offset_left = 80.0
offset_top = -201.0
offset_right = 310.0
offset_bottom = 46.0
grow_horizontal = 1
pivot_offset = Vector2(115, 123)
script = ExtResource("8_0tmbb")

[node name="Label" type="Label" parent="Menu/ChangeCharacterButton"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -32.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 3
theme_override_fonts/font = ExtResource("11_gn1ql")
theme_override_font_sizes/font_size = 25
text = "CHANGE_CHAR"
horizontal_alignment = 1

[node name="image" type="TextureRect" parent="Menu/ChangeCharacterButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -40.0
offset_bottom = -38.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("10_k8t2m")
expand_mode = 1
stretch_mode = 5

[node name="ChatButton" parent="Menu" instance=ExtResource("5_yqo4e")]
custom_minimum_size = Vector2(243.6, 60)
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
offset_left = -122.0
offset_top = -120.0
offset_right = 122.0
offset_bottom = -32.0
grow_vertical = 0
pivot_offset = Vector2(121, 44)
script = ExtResource("9_fvaof")

[node name="TextureRect" type="TextureRect" parent="Menu/ChatButton"]
layout_mode = 0
offset_left = 19.0
offset_top = 15.0
offset_right = 76.0
offset_bottom = 69.0
mouse_filter = 2
texture = ExtResource("9_cjknp")
expand_mode = 1
stretch_mode = 4

[node name="OnlineLabel" type="Label" parent="Menu/ChatButton"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 50.0
offset_top = -41.0
offset_right = 90.0
offset_bottom = -9.0
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 3
theme_override_fonts/font = ExtResource("11_gn1ql")
theme_override_font_sizes/font_size = 18
text = "+2"
horizontal_alignment = 1

[node name="newImage" type="NinePatchRect" parent="Menu/ChatButton"]
visible = false
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -33.0
offset_top = -15.0
offset_right = 5.0
offset_bottom = 21.0
grow_horizontal = 0
texture = ExtResource("14_4moyu")

[node name="Plus" type="Label" parent="Menu/ChatButton/newImage"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -3.5
offset_bottom = 5.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 3
theme_override_fonts/font = ExtResource("11_gn1ql")
theme_override_font_sizes/font_size = 26
text = "+"
horizontal_alignment = 1

[node name="ShopButton" parent="Menu" instance=ExtResource("5_yqo4e")]
custom_minimum_size = Vector2(230, 88)
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_right = 0.0
anchor_bottom = 0.5
offset_left = 80.0
offset_top = 76.0
offset_right = 310.0
offset_bottom = 164.0
grow_horizontal = 1
pivot_offset = Vector2(115, 44)

[node name="TextureRect" type="TextureRect" parent="Menu/ShopButton"]
layout_mode = 0
offset_left = 14.0
offset_top = 18.0
offset_right = 62.0
offset_bottom = 63.0
mouse_filter = 2
texture = ExtResource("7_gnheb")

[node name="SettingsButton" parent="Menu" instance=ExtResource("8_yp623")]
custom_minimum_size = Vector2(70, 70)
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -133.0
offset_top = 18.0
offset_right = -63.0
offset_bottom = 88.0
grow_horizontal = 0
grow_vertical = 1

[node name="CrownShow" parent="Menu" instance=ExtResource("20_wmpg2")]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -557.0
offset_top = 32.0
offset_right = -407.0
offset_bottom = 82.0
grow_horizontal = 0

[node name="CupShow" parent="Menu" instance=ExtResource("13_3g6nl")]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -437.0
offset_top = 32.0
offset_right = -287.0
offset_bottom = 82.0
grow_horizontal = 0
mouse_filter = 2

[node name="CoinShow" parent="Menu" instance=ExtResource("14_t3uge")]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -317.0
offset_top = 32.0
offset_right = -167.0
offset_bottom = 82.0
grow_horizontal = 0
grow_vertical = 1

[node name="ProgressButton" parent="Menu" instance=ExtResource("23_dlmr0")]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_right = 0.0
offset_left = 80.0
offset_top = -132.0
offset_right = 380.0
offset_bottom = -32.0
grow_horizontal = 1
grow_vertical = 0
pivot_offset = Vector2(200, 50)

[node name="PlayButton" type="Control" parent="Menu"]
custom_minimum_size = Vector2(175, 88)
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -247.0
offset_top = -120.0
offset_right = -72.0
offset_bottom = -32.0
grow_horizontal = 0
grow_vertical = 0
pivot_offset = Vector2(87.5, 44)
mouse_filter = 1
script = ExtResource("25_2e2w0")

[node name="Button" type="Button" parent="Menu/PlayButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(600, 360)
focus_mode = 0
mouse_filter = 1
theme_override_font_sizes/font_size = 40
theme_override_styles/focus = SubResource("StyleBoxEmpty_egcvn")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_fmyl7")
theme_override_styles/hover = SubResource("StyleBoxEmpty_7l4b6")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_8endb")
theme_override_styles/normal = SubResource("StyleBoxEmpty_6pnhn")
flat = true

[node name="TextureRectIMG" type="TextureRect" parent="Menu/PlayButton/Button"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("26_taxne")
expand_mode = 1

[node name="Label" type="Label" parent="Menu/PlayButton/Button"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/line_spacing = -10
theme_override_constants/outline_size = 6
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("11_gn1ql")
theme_override_font_sizes/font_size = 30
text = "Play"
horizontal_alignment = 1
vertical_alignment = 1

[node name="TaxParent" type="Control" parent="Menu/PlayButton"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="GameTax" type="Label" parent="Menu/PlayButton/TaxParent"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -35.0
offset_right = -90.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 3
theme_override_fonts/font = ExtResource("11_gn1ql")
theme_override_font_sizes/font_size = 20
text = "0"
horizontal_alignment = 2

[node name="TextureRect" type="TextureRect" parent="Menu/PlayButton/TaxParent"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -4.5
offset_top = -34.0
offset_right = 24.5
offset_bottom = -5.0
grow_horizontal = 2
grow_vertical = 0
mouse_filter = 2
texture = ExtResource("7_m2chx")
expand_mode = 1
stretch_mode = 4

[node name="GameModeSelectorButton" parent="Menu" instance=ExtResource("5_yqo4e")]
custom_minimum_size = Vector2(40, 40)
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
offset_left = -65.0
offset_top = -120.0
offset_right = -25.0
offset_bottom = -80.0
grow_horizontal = 0
grow_vertical = 0
pivot_offset = Vector2(20, 20)

[node name="UsernameLabel" type="Label" parent="Menu"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -254.0
offset_top = -114.5
offset_right = -82.0
offset_bottom = -47.5
grow_horizontal = 0
grow_vertical = 2
theme_override_fonts/font = ExtResource("11_gn1ql")
theme_override_font_sizes/font_size = 35
text = "USERNAME"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ClipboardButton" parent="Menu/UsernameLabel" instance=ExtResource("35_uqxyy")]
custom_minimum_size = Vector2(200, 50)
layout_mode = 1
offset_left = -18.0
offset_top = 57.0
offset_right = 10.0
offset_bottom = 70.0
pivot_offset = Vector2(100, 40)

[node name="TextureRect2" type="NinePatchRect" parent="Menu/UsernameLabel/ClipboardButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("36_8p6sa")
patch_margin_left = 32
patch_margin_top = 23
patch_margin_right = 29
patch_margin_bottom = 26

[node name="UsernameLabel" type="Label" parent="Menu/UsernameLabel/ClipboardButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 6
theme_override_constants/shadow_outline_size = 4
theme_override_fonts/font = ExtResource("11_gn1ql")
theme_override_font_sizes/font_size = 31
text = "username"
horizontal_alignment = 1
vertical_alignment = 1

[node name="InstagramButton" parent="Menu" instance=ExtResource("35_uqxyy")]
custom_minimum_size = Vector2(80, 80)
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = -256.0
offset_top = 32.0
offset_right = -176.0
offset_bottom = 112.0
grow_horizontal = 0
pivot_offset = Vector2(40, 40)

[node name="NinePatchRect" type="NinePatchRect" parent="Menu/InstagramButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("37_6371e")
patch_margin_left = 29
patch_margin_top = 23
patch_margin_right = 27
patch_margin_bottom = 29

[node name="TextureRect" type="TextureRect" parent="Menu/InstagramButton"]
custom_minimum_size = Vector2(55, 55)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -27.5
offset_top = -30.5
offset_right = 27.5
offset_bottom = 24.5
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("26_yjqb1")
expand_mode = 1
stretch_mode = 4

[node name="RubikaButton" parent="Menu" instance=ExtResource("35_uqxyy")]
custom_minimum_size = Vector2(80, 80)
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = -210.0
offset_top = 113.0
offset_right = -130.0
offset_bottom = 193.0
grow_horizontal = 0
pivot_offset = Vector2(40, 40)

[node name="NinePatchRect" type="NinePatchRect" parent="Menu/RubikaButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("37_6371e")
patch_margin_left = 29
patch_margin_top = 23
patch_margin_right = 27
patch_margin_bottom = 29

[node name="TextureRect" type="TextureRect" parent="Menu/RubikaButton"]
custom_minimum_size = Vector2(65, 65)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -32.5
offset_top = -36.5
offset_right = 32.5
offset_bottom = 28.5
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("38_pgx4r")
expand_mode = 1
stretch_mode = 4

[node name="AparatButton" parent="Menu" instance=ExtResource("35_uqxyy")]
custom_minimum_size = Vector2(80, 80)
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = -166.0
offset_top = 33.0
offset_right = -86.0
offset_bottom = 113.0
grow_horizontal = 0
pivot_offset = Vector2(40, 40)

[node name="NinePatchRect" type="NinePatchRect" parent="Menu/AparatButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("37_6371e")
patch_margin_left = 29
patch_margin_top = 23
patch_margin_right = 27
patch_margin_bottom = 29

[node name="TextureRect" type="TextureRect" parent="Menu/AparatButton"]
custom_minimum_size = Vector2(65, 65)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -32.5
offset_top = -36.5
offset_right = 32.5
offset_bottom = 28.5
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("39_unswq")
expand_mode = 1
stretch_mode = 4

[node name="NamePopup" parent="Menu" instance=ExtResource("5_ep3jm")]
visible = false
layout_mode = 1

[node name="SettingsPopup" parent="Menu" instance=ExtResource("9_xpvwf")]
visible = false
layout_mode = 1
metadata/_edit_lock_ = true

[node name="LeaderboardPopup" parent="Menu" instance=ExtResource("11_k7af7")]
visible = false
layout_mode = 1

[node name="ProgressPopup" parent="Menu" instance=ExtResource("24_vf3d6")]
visible = false
layout_mode = 1

[node name="Label" type="Label" parent="Menu"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = 112.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 11
theme_override_font_sizes/font_size = 42
horizontal_alignment = 1

[node name="RatePopup" parent="Menu" instance=ExtResource("16_81y72")]
visible = false
layout_mode = 1

[node name="VPNPopup" parent="Menu" instance=ExtResource("20_siohu")]
visible = false
layout_mode = 1

[node name="NotEnoughCoinsPopup" parent="Menu" instance=ExtResource("30_8st3f")]
visible = false
layout_mode = 1

[node name="DisconnectPopup" parent="Menu" instance=ExtResource("30_t5jej")]
visible = false
layout_mode = 1

[node name="ChatTutorialPopup" parent="Menu" instance=ExtResource("32_vewh7")]
visible = false
layout_mode = 1

[node name="PublicChatPopup" parent="Menu" instance=ExtResource("21_6f4v1")]
visible = false
layout_mode = 1

[node name="ExitPopup" parent="Menu" instance=ExtResource("17_l5v2e")]
visible = false
layout_mode = 1
hide_settings = true

[node name="GiftPopup" parent="Menu" instance=ExtResource("43_rohdu")]
visible = false
layout_mode = 1

[node name="LoadingPanel" type="Panel" parent="Menu"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = 20.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_l80g8")

[node name="Label" type="Label" parent="Menu/LoadingPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 11
theme_override_fonts/font = ExtResource("11_gn1ql")
theme_override_font_sizes/font_size = 80
text = "LOADING"
horizontal_alignment = 1
vertical_alignment = 1

[node name="UpdatePopup" parent="Menu" instance=ExtResource("36_an2a6")]
visible = false
layout_mode = 1

[node name="CharacterSelect" parent="." instance=ExtResource("2_kayod")]
visible = false
layout_mode = 1

[node name="HintParent" type="Control" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
script = ExtResource("31_xx3io")
metadata/_edit_lock_ = true

[node name="TutorialHint" parent="HintParent" node_paths=PackedStringArray("focus_node") instance=ExtResource("31_tx7vi")]
visible = false
layout_mode = 1
focus_node = NodePath("../../Menu/PlayButton")

[node name="Label" type="Label" parent="HintParent/TutorialHint"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 167.0
offset_right = -334.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_constants/shadow_outline_size = 8
theme_override_fonts/font = ExtResource("11_gn1ql")
theme_override_font_sizes/font_size = 60
text = "HINT_TUTORIAL"
horizontal_alignment = 1
vertical_alignment = 1

[node name="FirstGameHint" parent="HintParent" node_paths=PackedStringArray("focus_node") instance=ExtResource("31_tx7vi")]
visible = false
layout_mode = 1
focus_node = NodePath("../../Menu/PlayButton")

[node name="Label" type="Label" parent="HintParent/FirstGameHint"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 167.0
offset_right = -334.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_constants/shadow_outline_size = 8
theme_override_fonts/font = ExtResource("11_gn1ql")
theme_override_font_sizes/font_size = 60
text = "HINT_FIRST_GAME"
horizontal_alignment = 1
vertical_alignment = 1

[node name="DailyRewardHint" parent="HintParent" instance=ExtResource("31_tx7vi")]
visible = false
layout_mode = 1

[node name="Label" type="Label" parent="HintParent/DailyRewardHint"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 7.0
offset_right = -334.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_constants/shadow_outline_size = 8
theme_override_fonts/font = ExtResource("11_gn1ql")
theme_override_font_sizes/font_size = 60
text = "HINT_DAILY_REWARD"
horizontal_alignment = 1
vertical_alignment = 1

[node name="CharacterHint" parent="HintParent" node_paths=PackedStringArray("focus_node") instance=ExtResource("31_tx7vi")]
visible = false
layout_mode = 1
focus_node = NodePath("../../Menu/ChangeCharacterButton")

[node name="Label" type="Label" parent="HintParent/CharacterHint"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 429.0
offset_right = -8.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_constants/shadow_outline_size = 8
theme_override_fonts/font = ExtResource("11_gn1ql")
theme_override_font_sizes/font_size = 60
text = "HINT_CHANGE_CHAR"
horizontal_alignment = 1
vertical_alignment = 1

[node name="SecondGameHint" parent="HintParent" node_paths=PackedStringArray("focus_node") instance=ExtResource("31_tx7vi")]
visible = false
layout_mode = 1
focus_node = NodePath("../../Menu/PlayButton")

[node name="Label" type="Label" parent="HintParent/SecondGameHint"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 167.0
offset_right = -334.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_constants/shadow_outline_size = 8
theme_override_fonts/font = ExtResource("11_gn1ql")
theme_override_font_sizes/font_size = 60
text = "HINT_SECOND_GAME"
horizontal_alignment = 1
vertical_alignment = 1

[connection signal="gui_input" from="Menu/CharacterPreviewImage" to="Menu/CharacterPreviewImage" method="_on_full_screen_gui_input"]
[connection signal="pressed" from="Menu/CharacterPreviewImage/ClanButton/ClanBadge" to="." method="_on_clan_badge_pressed"]
[connection signal="pressed" from="Menu/NameButton" to="." method="_on_name_button_pressed"]
[connection signal="pressed" from="Menu/LeaderboardButton" to="." method="_on_leaderboard_button_pressed"]
[connection signal="pressed" from="Menu/ClanButton" to="." method="_on_clan_button_pressed"]
[connection signal="pressed" from="Menu/ChangeCharacterButton" to="." method="_on_change_character_button_pressed"]
[connection signal="pressed" from="Menu/ChatButton" to="." method="_on_chat_button_pressed"]
[connection signal="pressed" from="Menu/ShopButton" to="." method="_on_shop_button_pressed"]
[connection signal="pressed" from="Menu/SettingsButton" to="." method="_on_settings_button_pressed"]
[connection signal="pressed" from="Menu/ProgressButton" to="." method="_on_progress_button_pressed"]
[connection signal="item_rect_changed" from="Menu/PlayButton" to="Menu/PlayButton" method="_on_item_rect_changed"]
[connection signal="pressed" from="Menu/PlayButton" to="." method="_on_play_button_pressed"]
[connection signal="button_down" from="Menu/PlayButton/Button" to="Menu/PlayButton" method="_on_button_button_down"]
[connection signal="button_up" from="Menu/PlayButton/Button" to="Menu/PlayButton" method="_on_button_button_up"]
[connection signal="pressed" from="Menu/PlayButton/Button" to="Menu/PlayButton" method="_on_button_pressed"]
[connection signal="pressed" from="Menu/GameModeSelectorButton" to="." method="_on_game_mode_selector_button_pressed"]
[connection signal="pressed" from="Menu/UsernameLabel/ClipboardButton" to="." method="_on_clipboard_button_pressed"]
[connection signal="pressed" from="Menu/InstagramButton" to="." method="_on_instagram_button_pressed"]
[connection signal="pressed" from="Menu/RubikaButton" to="." method="_on_rubika_button_pressed"]
[connection signal="pressed" from="Menu/AparatButton" to="." method="_on_aparat_button_pressed"]
[connection signal="hidden" from="Menu/NamePopup" to="." method="_on_name_popup_hidden"]
[connection signal="on_name_changed" from="Menu/NamePopup" to="." method="_on_name_popup_on_name_changed"]
[connection signal="music_toggled" from="Menu/SettingsPopup" to="." method="_on_settings_popup_music_toggled"]
[connection signal="play" from="Menu/VPNPopup" to="." method="_on_vpn_popup_play"]
[connection signal="hidden" from="Menu/ChatTutorialPopup" to="." method="_on_chat_tutorial_popup_hidden"]
[connection signal="character_selected" from="CharacterSelect" to="." method="_on_character_select_character_selected"]
[connection signal="finished" from="CharacterSelect" to="." method="_on_character_select_finished"]
[connection signal="hidden" from="CharacterSelect" to="." method="_on_character_select_hidden"]
